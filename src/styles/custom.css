/* Custom CSS for SEVO Website */

/* Import Google Fonts for Arabic text */
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Inter:wght@300;400;500;600;700&display=swap');

/* Root variables for consistent theming */
:root {
  --sevo-brown: #5A3A28;
  --sevo-cream: #F5EFE6;
  --sevo-orange: #FF8C42;
  --sevo-green: #16A34A;
  --sevo-blue: #2563EB;
  --sevo-purple: #9333EA;
}

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

/* Arabic text styling */
.arabic-text {
  font-family: '<PERSON><PERSON>', 'Times New Roman', serif;
  direction: rtl;
  text-align: center;
  font-weight: 400;
  line-height: 1.8;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--sevo-brown);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--sevo-orange);
}

/* Animation classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Hero section enhancements */
.hero-overlay {
  background: linear-gradient(135deg, rgba(90,58,40,0.7), rgba(0,0,0,0.5));
  backdrop-filter: blur(1px);
}

/* Navigation enhancements */
nav {
  backdrop-filter: blur(10px);
  background: rgba(90, 58, 40, 0.95);
}

/* Button hover effects */
.btn-hover {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-hover:hover::before {
  left: 100%;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Gallery image effects */
.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
}

.gallery-item img {
  transition: transform 0.5s ease;
}

.gallery-item:hover img {
  transform: scale(1.1);
}

/* Stats counter animation */
.stat-counter {
  font-variant-numeric: tabular-nums;
}

/* Loading animation for images */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive typography */
@media (max-width: 768px) {
  .arabic-text {
    font-size: 1.1rem;
  }
  
  h1 {
    font-size: 2.5rem !important;
  }
  
  h2 {
    font-size: 2rem !important;
  }
}

/* Focus styles for accessibility */
a:focus,
button:focus {
  outline: 2px solid var(--sevo-orange);
  outline-offset: 2px;
}

/* Skip link styles */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 0.5rem 1rem;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Print styles */
@media print {
  .fixed,
  nav,
  footer,
  #scrollToTop {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  h1, h2, h3 {
    page-break-after: avoid;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --sevo-brown: #000000;
    --sevo-cream: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}
