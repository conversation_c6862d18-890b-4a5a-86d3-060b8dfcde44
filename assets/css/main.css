/* General Body Styles */
body {
    margin: 0;
    font-family: 'Poppins', sans-serif;
    background-color: #fdfaf6;
}

/* Navbar Styles */
.navbar {
    position: sticky;
    top: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fcebd8;
    padding: 1rem 2rem;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.navbar .logo img {
    height: 50px;
}

.navbar .menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center; /* Center the menu items */
    flex-grow: 1; /* Allow the menu to grow and occupy space */
}

.navbar .menu li {
    margin: 0 1.5rem; /* Adjusted for centered layout */
}

.navbar .menu a {
    text-decoration: none;
    color: #5a3e2a;
    font-weight: bold;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.navbar .menu a:hover {
    color: #c6895c;
}

/* Hero Section Styles */
.hero {
    background-color: #c6895c;
    color: #fcebd8;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    padding: 1.6rem 5rem;
    min-height: 80vh;
}

.hero-text {
    padding-right: 3rem;
}

.hero-text h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.hero-text p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.hero-text .btn {
    background-color: #fcebd8;
    color: #c6895c;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    transition: transform 0.3s ease, background-color 0.3s ease;
    border: 2px solid transparent;
}

.hero-text .btn:hover {
    transform: scale(1.05);
    background-color: #ffffff;
    color: #b57a50;
}

.hero-image img {
    max-width: 100%;
    height: auto;
}

/* About Us Section */
.about-us-section {
    background-color: #FDF8F0;
    padding: 100px 0;
    font-family: 'Inter', sans-serif;
    min-height: 65vh;
    display: flex;
    align-items: center;
}

.about-us-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

.about-us-title {
    text-align: center;
    font-family: 'Lora', serif;
    font-size: 3rem;
    color: #333333;
    margin-bottom: 60px;
}

.about-us-content {
    display: flex;
    gap: 60px;
    align-items: center;
}

.about-us-left {
    width: 40%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.about-us-left h2 {
    font-family: 'Lora', serif;
    font-size: 2.5rem;
    color: #333333;
    margin-bottom: 25px;
}

.about-us-left p {
    color: #5D5D5D;
    line-height: 1.7;
    margin-bottom: 35px;
    font-size: 1.2rem;
}

.about-us-button {
    background-color: #C19A6B;
    color: #FFFFFF;
    padding: 15px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
    align-self: flex-start;
}

.about-us-button:hover {
    background-color: #a8855a;
}

.about-us-right {
    width: 60%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.focus-area-item {
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.focus-area-item:hover {
    transform: scale(1.05);
}

.focus-area-item:hover .focus-area-label {
    color: #333333;
}

.focus-icon {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    flex-shrink: 0;
}

.focus-area-label {
    color: #5D5D5D;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

/* Feature Sections */
.feature-section {
    padding: 100px 0;
    min-height: 65vh;
    display: flex;
    align-items: center;
}

.feature-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

.feature-content {
    display: flex;
    gap: 60px;
    align-items: center;
}

.feature-text {
    flex: 1;
}

.feature-image {
    flex: 1;
}

.feature-image img {
    width: 100%;
    border-radius: 12px;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.feature-text h2 {
    font-family: 'Lora', serif;
    font-size: 2.5rem;
    color: #333333;
    margin-bottom: 25px;
}

.feature-text p {
    color: #5D5D5D;
    line-height: 1.7;
    margin-bottom: 35px;
    font-size: 1.2rem;
}

.feature-section.layout-reversed .feature-content {
    flex-direction: row-reverse;
}

/* Hamburger Menu */
.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    height: 3px;
    width: 25px;
    background-color: #333;
    margin: 4px 0;
    transition: 0.4s;
}

/* Responsive Design */
@media (max-width: 992px) {
    .about-us-content, .feature-content {
        gap: 40px;
    }
    .focus-icon {
        width: 80px;
        height: 80px;
    }
    .focus-area-label {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    /* Navbar Responsive */
    .navbar {
        flex-direction: row; /* Keep logo and toggle inline */
        flex-wrap: wrap;
    }
    .navbar .menu {
        display: none;
        width: 100%;
        text-align: center;
        flex-direction: column;
    }
    .navbar .menu.active {
        display: flex; /* Show menu when active */
    }
    .menu-toggle {
        display: flex;
    }

    /* Hero Section Responsive */
    .hero {
        grid-template-columns: 1fr;
        padding: 2rem;
        text-align: center;
    }
    .hero-text {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    .hero-text h1 {
        font-size: 2rem;
    }
    .hero-text p {
        font-size: 1rem;
    }

    /* About Us Section Responsive */
    .about-us-section {
        padding: 60px 0;
        min-height: auto;
    }
    .about-us-content {
        flex-direction: column;
    }
    .about-us-left,
    .about-us-right {
        width: 100%;
    }
    .about-us-right {
        grid-template-columns: 1fr;
        margin-top: 40px;
    }
    .about-us-left {
        align-items: center;
        text-align: center;
    }
    .about-us-button {
        align-self: center;
    }
    .focus-area-item {
        flex-direction: row; /* Ensure items are rows */
        justify-content: flex-start; /* Align to the start */
        gap: 20px; /* Consistent gap */
    }

    /* Feature Sections Responsive */
    .feature-section {
        padding: 60px 0;
        min-height: auto;
    }
    .feature-content {
        flex-direction: column;
    }
    .feature-section.layout-reversed .feature-content {
        flex-direction: column-reverse;
    }
    .feature-text {
        text-align: center;
    }
    .feature-text .btn {
        align-self: center;
    }
}


* { box-sizing: border-box; }

/*body {
    font-family: "Lato", sans-serif;
    line-height: $line-height;
    background-color: $bg-color;
    color: $text-color;
    font-weight: 500;
}
*/
.container {
    max-width: 1280px;
    margin: 3rem auto;
    padding: 1rem;
}

h1 {
    text-align: center;
}

button {
    font-size: 1rem;
    padding: 0.35em 0.75em;
    line-height: 1;
    background-color: transparent;
    border: 0.125rem solid $text-color;
    border-radius: 2rem;
    cursor: pointer;
    transition: 0.1s;
    outline: 0;
    
    &:hover {
        background-color: $text-color;
        color: #fff;
    }
    
    .fa {
        font-size: 0.75em;
        margin-left: 0.5em;
    }
    
    &.btn--primary {
        border-color: $primary;
        color: $primary;
        
        &:hover {
            background-color: $primary;
            color: #fff;
        }
    }
    
    &.btn--accent {
        border-color: $accent;
        color: $accent;
        
        &:hover {
            background-color: $accent;
            color: #fff;
        }
    }
}

.posts {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 2.5rem;
    
    @media(max-width: 1140px) {
        grid-template-columns: repeat(2, 1fr);
    }
    
    @media(max-width: 768px) {
        grid-template-columns: repeat(1, 1fr);
    }
}


$excerpt-height: 5rem * $line-height;
// title (fontSize + marginBottom) + excerpt + button (marginTop + 2 * paddingTopBottom + fontSize + borderWidth) + safe-space
$content-height: (1.35rem + 1rem) + $excerpt-height + (1rem + 2 * 0.35rem + 1rem ) + 0.25rem + 0.1rem;
// title (fontSize + marginBottom) + excerpt (2 lines)
$content-height-collapsed: (1.35rem + 1rem) + (2rem * $line-height);
$content-overlap-collapsed: 3rem;
$content-overlap: ($content-height - $content-height-collapsed) + $content-overlap-collapsed;

.post {
    
    &__image {
        width: 100%;
        height: 240px;
        position: relative;
        overflow: hidden;
        
        &:before,
        &:after {
            content: '';
            display: block;
            position: absolute;
            top: 0; left: 0; bottom: 0; right: 0;
            width: 100%; height: 100%; 
        }
        
        // image
        &:before {
            background-size: cover;
            background-position: center center;
            transform: scale(1);
            filter: blur(0);
            transition: 2s cubic-bezier(0, 0.6, 0.2, 1);
        }
        
        // overlay
        &:after {
            // background-color: $primary;
            
            background: linear-gradient(30deg, $primary 0%, $accent 100%);
            background-size: 100% 300%;
            background-position: bottom left;
            opacity: 0.15;
            transition: 2s cubic-bezier(0, 0.6, 0.2, 1);
        }
        
        &--1:before { background-image: url('https://images.unsplash.com/photo-1510951459752-aac634df6e86?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=50bdf8b5068e794a82c849cc7e269ed3'); }
        &--2:before { background-image: url('https://images.unsplash.com/photo-1529392960549-df4af50eac23?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=b482040f9d3a25a5e5352948f68f3a0e'); }
        &--3:before { background-image: url('https://images.unsplash.com/photo-1506258998-82810ddc75a3?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=68da264c24bc024a0b2ff92c349e89ed'); }
        &--4:before { background-image: url('https://images.unsplash.com/photo-1520875777965-f99b03dc86e8?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=34ff37a297e7e9e7be972356103b6750'); }
        &--5:before { background-image: url('https://images.unsplash.com/photo-1527664557558-a2b352fcf203?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=d06ac80d600822cb010987a6af4ff02a'); }
        &--6:before { background-image: url('https://images.unsplash.com/photo-1507679799987-c73779587ccf?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=f982b6bf45d8a72d038b60a010e16767'); }
    }
    
    &__content {
        margin: -1 * $content-overlap-collapsed 1.5rem 0;
        padding: 1.5rem;
        background-color: #fff;
        border-radius: 3px;
        box-shadow: 0 1rem 3rem rgba(0,0,0,0.15);
        transition: margin 0.2s ease-in-out;
        position: relative;
        z-index: 1;
        user-select: none;
    }
    
    &__inside {
        overflow: hidden;
        height: $content-height-collapsed;
        transition: height 0.2s ease-in-out;
    }
    
    &__title {
        font-size: 1.35rem;
        line-height: 1;
        margin: 0 0 1rem;
        font-weight: 300;
        color: $primary;
    }
    
    &__excerpt {
        overflow: hidden;
        margin: 0;
        max-height: $excerpt-height;
        position: relative;
    }
    
    &__button {
        margin-top: 1rem;
    }
    
    
}

/* ====== HOVER ====== */

.post:hover {
    .post {
        &__content {
            margin-top: -1 * $content-overlap;
        }
        
        &__inside {
            height: $content-height;
        }
        
        &__image {
            &:after { opacity: 0.5; }
            
            &:before {
                transform: scale(1.1);
                filter: blur(10px);
            }
            
        }
    }
}