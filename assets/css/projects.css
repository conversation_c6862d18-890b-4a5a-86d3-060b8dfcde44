.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 4rem 2rem;
}

.project-card {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
}

.project-card h3 {
    font-family: 'Lexend', sans-serif;
    color: #333;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.project-card p {
    font-family: 'Lexend', sans-serif;
    color: #666;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}
