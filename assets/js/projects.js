document.addEventListener('DOMContentLoaded', () => {
    const projectsGrid = document.querySelector('.projects-grid');

    fetch('../../data/projects.json')
        .then(response => response.json())
        .then(data => {
            data.projects.forEach(project => {
                const projectCard = document.createElement('div');
                projectCard.classList.add('project-card');

                projectCard.innerHTML = `
                    <h3>${project.name}</h3>
                    <p><strong>Location:</strong> ${project.location}</p>
                    <p><strong>Category:</strong> ${project.category}</p>
                    <p><strong>Status:</strong> ${project.status}</p>
                    <p>${project.description}</p>
                `;

                projectsGrid.appendChild(projectCard);
            });
        })
        .catch(error => console.error('Error fetching projects:', error));
});
