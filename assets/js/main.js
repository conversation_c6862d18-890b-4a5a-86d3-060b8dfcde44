const menuToggle = document.getElementById('mobile-menu');
const navLinks = document.querySelector('.nav-links');

menuToggle.addEventListener('click', () => {
    navLinks.classList.toggle('active');
});

// Fetch and display news data
async function loadNews() {
    try {
        const response = await fetch('../../data/news.json');
        const data = await response.json();
        displayNews(data.news);
    } catch (error) {
        console.error('Error loading news:', error);
        // Fallback to static content if fetch fails
    }
}

function displayNews(newsItems) {
    const postsContainer = document.querySelector('.posts');
    if (!postsContainer) return;

    // Clear existing posts
    postsContainer.innerHTML = '';

    // Create news cards
    newsItems.forEach((newsItem, index) => {
        const postCard = createNewsCard(newsItem, index + 1);
        postsContainer.appendChild(postCard);
    });
}

function createNewsCard(newsItem, imageIndex) {
    const postDiv = document.createElement('div');
    postDiv.className = 'post';

    postDiv.innerHTML = `
        <div class="post__image post__image--${imageIndex}" style="background-image: url('../../assets/${newsItem.image}')"></div>
        <div class="post__content">
            <div class="post__inside">
                <div class="post__date" style="color: var(--sevo-orange); font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem;">${newsItem.date}</div>
                <h3 class="post__title">${newsItem.title}</h3>
                <p class="post__excerpt">${newsItem.excerpt}</p>
                <button class="btn--accent post__button">
                    Read more
                    <i class="fa fa-chevron-right"></i>
                </button>
            </div>
        </div>
    `;

    return postDiv;
}

// Load news when page loads
document.addEventListener('DOMContentLoaded', loadNews);
