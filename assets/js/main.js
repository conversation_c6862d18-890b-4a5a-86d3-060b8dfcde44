const menuToggle = document.getElementById('mobile-menu');
const navLinks = document.querySelector('.nav-links');

menuToggle.addEventListener('click', () => {
    navLinks.classList.toggle('active');
});

// Fetch and display news data
async function loadNews() {
    try {
        const response = await fetch('../../data/news.json');
        const data = await response.json();
        displayNews(data.news);
    } catch (error) {
        console.error('Error loading news:', error);
        // Fallback to static content if fetch fails
    }
}

function displayNews(newsItems) {
    const postsContainer = document.querySelector('.posts');
    if (!postsContainer) return;

    // Clear existing posts
    postsContainer.innerHTML = '';

    // Create news cards
    newsItems.forEach((newsItem, index) => {
        const postCard = createNewsCard(newsItem, index + 1);
        postsContainer.appendChild(postCard);
    });
}

function createNewsCard(newsItem, imageIndex) {
    const postDiv = document.createElement('div');
    postDiv.className = 'post';

    postDiv.innerHTML = `
        <div class="post__image post__image--${imageIndex}" style="background-image: url('../../assets/${newsItem.image}')"></div>
        <div class="post__content">
            <div class="post__inside">
                <div class="post__date" style="color: var(--sevo-orange); font-size: 0.85rem; font-weight: 600; margin-bottom: 0.5rem;">${newsItem.date}</div>
                <h3 class="post__title">${newsItem.title}</h3>
                <p class="post__excerpt">${newsItem.excerpt}</p>
                <button class="btn--accent post__button">
                    Read more
                    <i class="fa fa-chevron-right"></i>
                </button>
            </div>
        </div>
    `;

    return postDiv;
}

// Fetch and display stats data
async function loadStats() {
    try {
        const response = await fetch('../../data/stats.json');
        const data = await response.json();
        displayStats(data.stats);
    } catch (error) {
        console.error('Error loading stats:', error);
        // Hide loading state if fetch fails
        const loadingElement = document.querySelector('.stats-loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }
}

function displayStats(statsItems) {
    const statsContainer = document.getElementById('stats-container');
    if (!statsContainer) return;

    // Clear loading state
    statsContainer.innerHTML = '';

    // Create stats cards
    statsItems.forEach((stat, index) => {
        const statCard = createStatCard(stat, index);
        statsContainer.appendChild(statCard);
    });
}

function createStatCard(stat, index) {
    const statDiv = document.createElement('div');
    statDiv.className = 'stat-card';
    statDiv.style.cssText = `
        background: rgba(245, 239, 230, 0.1);
        border: 2px solid rgba(245, 239, 230, 0.2);
        border-radius: 15px;
        padding: 30px 20px;
        text-align: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    `;

    // Add hover effects
    statDiv.addEventListener('mouseenter', function() {
        this.style.background = 'rgba(245, 239, 230, 0.2)';
        this.style.borderColor = '#FF8C42';
        this.style.transform = 'translateY(-5px)';
    });

    statDiv.addEventListener('mouseleave', function() {
        this.style.background = 'rgba(245, 239, 230, 0.1)';
        this.style.borderColor = 'rgba(245, 239, 230, 0.2)';
        this.style.transform = 'translateY(0)';
    });

    statDiv.innerHTML = `
        <div style="font-size: 3rem; margin-bottom: 15px;">${stat.icon}</div>
        <div style="font-size: 2.5rem; font-weight: bold; color: #FF8C42; margin-bottom: 10px;">${stat.value}+</div>
        <div style="font-size: 1.1rem; color: #F5EFE6; opacity: 0.9;">${stat.label}</div>
    `;

    return statDiv;
}

// Load both news and stats when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadNews();
    loadStats();
});
