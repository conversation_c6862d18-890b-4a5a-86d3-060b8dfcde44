# SEVO - Simat Elkhair Voluntary Organization Website

A beautiful, responsive homepage for **SEVO (Simat Elkhair Voluntary Organization)** — a Sudanese non-profit organization focused on empowering women, building water wells, and aiding vulnerable communities.

## 🌟 Features

- **Responsive Design**: Fully responsive layout that works on all devices
- **Islamic Values**: Design reflects Islamic values and Sudanese aesthetics
- **Accessibility**: Built with accessibility in mind (WCAG compliant)
- **Modern Technologies**: HTML5, Tailwind CSS 3+, Alpine.js
- **Interactive Elements**: Animated counters, smooth scrolling, hover effects
- **Semantic HTML**: Proper semantic structure for SEO and accessibility

## 🎨 Design Elements

### Color Palette
- **Primary Brown**: `#5A3A28` (SEVO Brown)
- **Cream Background**: `#F5EFE6` (SEVO Cream)
- **Accent Orange**: `#FF8C42`
- **Supporting Colors**: Green, Blue, Purple for different sections

### Typography
- **Primary Font**: Inter (modern, clean)
- **Arabic Text**: <PERSON><PERSON> (traditional Arabic font)
- **Responsive Typography**: Scales appropriately on all devices

## 📱 Sections

1. **Navigation Bar**
   - Fixed top navigation with logo
   - Mobile-responsive hamburger menu
   - Smooth scroll to sections

2. **Hero Section**
   - Full-screen background with overlay
   - Quranic verse in Arabic with translation
   - Call-to-action buttons

3. **About Us (Introduction Blocks)**
   - Two-column layout with images and text
   - Women's empowerment focus
   - Community service highlights

4. **Photo Gallery**
   - Grid layout with hover effects
   - Project showcase images
   - Responsive image grid

5. **Statistics Section**
   - Animated counters using Alpine.js
   - Key impact metrics
   - Islamic quote integration

6. **Footer**
   - Contact information
   - Social media links
   - Program listings
   - Multi-column responsive layout

## 🚀 Getting Started

### Prerequisites
- Modern web browser
- Local web server (optional, for development)

### Installation

1. Clone or download the project files
2. Open `index.html` in your web browser
3. For development, use a local server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

### File Structure
```
website/
├── index.html              # Main homepage
├── data/
│   └── stats.json         # Organization statistics and data
├── public/
│   └── images/
│       ├── logo.svg       # SEVO logo
│       └── [project images] # Placeholder for project photos
├── src/
│   └── styles/
│       └── custom.css     # Custom CSS styles
├── tailwind.config.js     # Tailwind CSS configuration
└── README.md             # This file
```

## 🛠️ Technologies Used

- **HTML5**: Semantic markup
- **Tailwind CSS 3+**: Utility-first CSS framework
- **Alpine.js**: Lightweight JavaScript framework for interactivity
- **Google Fonts**: Amiri (Arabic) and Inter fonts
- **SVG**: Scalable vector graphics for icons and logo

## 📊 Data Integration

The website uses `data/stats.json` to store:
- Organization statistics
- Program information
- Contact details
- Social media links

## 🎯 Accessibility Features

- Semantic HTML structure
- Skip navigation link
- Proper heading hierarchy
- Alt text for images
- Keyboard navigation support
- High contrast mode support
- Reduced motion support
- Screen reader friendly

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 🤝 Contributing

This website was built for SEVO (Simat Elkhair Voluntary Organization). For updates or modifications, please contact the organization directly.

## 📄 License

This project is created for SEVO - Simat Elkhair Voluntary Organization. All rights reserved.

## 📞 Contact

**SEVO - Simat Elkhair Voluntary Organization**
- **Location**: Nyala, South Darfur, Sudan
- **Phone**: +249 912 476 429
- **Email**: <EMAIL>
- **Website**: www.sevo.org

---

*Built with ❤️ by Mohamed Musa | Empowering communities through faith and action*
