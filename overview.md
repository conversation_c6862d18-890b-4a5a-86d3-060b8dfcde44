Your Role: You are an expert front-end web developer. Your task is to write the HTML and CSS for three new "feature" sections that will be placed directly after an existing "About Us" section on a webpage.

Primary Goal:
Create three full-width ections have the same hight as the about us section after the about us section in index_en.html . Each section will have a two-column layout containing a text block on one side and an image on the other. The layout must alternate for each section to create a dynamic, visually engaging page.
Design & Content Specifications

1. General Section Structure (for all three sections):

    Each section should be a <section> element with a class like feature-section.

    Inside, use a .container div to manage the width.

    The layout should be a two-column grid or flexbox layout. Each column (one for text, one for image) should take up equal space.

    The sections should have significant vertical padding (e.g., 100px top and bottom) to feel as substantial as the "About Us" section.

2. Text Column Content (for all three sections):

    An <h2> heading (NOT <h1>) for the section title.

    A paragraph <p> with descriptive text.

    An anchor tag <a> styled as a button for the call to action.

3. Image Column Content (for all three sections):

    An <img> tag. Use placeholder images from a service like https://placehold.co/.

    The image should have a subtle box-shadow and rounded corners (border-radius: 12px).

4. Alternating Layout Logic:

    Section 1: Text on the left, Image on the right.

    Section 2: Image on the left, Text on the right. This should be achieved by adding a modifier class (e.g., .layout-reversed) to the main <section> element, which reverses the column order using CSS flex-direction: row-reverse;.

    Section 3: Text on the left, Image on the right (back to the default order).

5. Specific Content:

    Section 1 (Text Left):

        Heading: "Empowering Through Education"

        Paragraph: "We believe knowledge is power. Our programs provide access to quality education for children and adults, opening doors to brighter futures and breaking cycles of poverty."

        Button Text: "Our Education Initiatives"

        Image: A placeholder representing education.

    Section 2 (Text Right):

        Heading: "Sustainable Community Growth"

        Paragraph: "From clean water projects to agricultural training, we work hand-in-hand with communities to build self-sufficient and resilient local economies that thrive for generations."

        Button Text: "See Community Projects"

        Image: A placeholder representing community.

        Note: This section should also have a slightly different background color (e.g., #f7f1e9) to make it stand out.

    Section 3 (Text Left):

        Heading: "Health and Wellness for All"

        Paragraph: "Access to healthcare is a fundamental right. We establish clinics, run mobile health camps, and promote wellness education to ensure every individual has the chance to live a healthy life."

        Button Text: "Explore Health Programs"

        Image: A placeholder representing healthcare.

6. Responsiveness (Mobile View - below 768px):

    The two columns in each section must stack vertically.

    For standard sections, the text column should appear above the image column.

    Crucially, for the .layout-reversed section, the columns should also stack, but the visual order should be consistent: Image first, then Text below it. This provides a better user experience on mobile.