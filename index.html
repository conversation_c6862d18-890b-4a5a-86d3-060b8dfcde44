<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEVO - Simat Elkhair Voluntary Organization</title>
    <meta name="description" content="SEVO empowers women, builds water wells, and aids vulnerable communities in Sudan with Islamic values and grassroots authenticity.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/src/styles/custom.css">
    
    <!-- Alpine.js for interactive counters -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sevo-brown': '#5A3A28',
                        'sevo-cream': '#F5EFE6',
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS for smooth scrolling and animations -->
    <style>
        html {
            scroll-behavior: smooth;
        }
        
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }
        
        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Arabic text styling */
        .arabic-text {
            font-family: 'Amiri', 'Times New Roman', serif;
            direction: rtl;
            text-align: center;
        }
        
        /* Hero background overlay animation */
        .hero-overlay {
            background: linear-gradient(45deg, rgba(0,0,0,0.6), rgba(90,58,40,0.4));
        }
        
        /* Mobile menu styles */
        .mobile-menu {
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .mobile-menu.open {
            transform: translateX(0);
        }
    </style>
</head>
<body class="font-sans">
    <!-- Skip Navigation Link for Accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white p-2 z-50">
        Skip to main content
    </a>

    <!-- Navigation Bar -->
    <nav class="bg-sevo-brown text-white fixed top-0 w-full z-50 shadow-md" x-data="{ mobileMenuOpen: false }">
        <div class="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
            <!-- Logo -->
            <div class="flex items-center">
                <img src="/public/images/logo.svg" alt="SEVO Logo" class="h-10 w-10 mr-2" onerror="this.style.display='none'">
                <span class="font-bold text-xl">SEVO</span>
            </div>
            
            <!-- Desktop Menu -->
            <ul class="hidden md:flex gap-8 uppercase tracking-wide text-sm font-medium">
                <li><a href="#" class="hover:text-orange-400 transition-colors duration-200">Home</a></li>
                <li><a href="#about" class="hover:text-orange-400 transition-colors duration-200">About Us</a></li>
                <li><a href="#projects" class="hover:text-orange-400 transition-colors duration-200">Projects</a></li>
                <li><a href="#gallery" class="hover:text-orange-400 transition-colors duration-200">Gallery</a></li>
                <li><a href="#contact" class="hover:text-orange-400 transition-colors duration-200">Contact Us</a></li>
            </ul>
            
            <!-- Mobile Menu Button -->
            <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden focus:outline-none focus:ring-2 focus:ring-orange-400 p-2">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
        
        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen" x-transition class="md:hidden bg-sevo-brown border-t border-orange-400">
            <ul class="px-4 py-4 space-y-3 uppercase tracking-wide text-sm font-medium">
                <li><a href="#" @click="mobileMenuOpen = false" class="block hover:text-orange-400 transition-colors duration-200">Home</a></li>
                <li><a href="#about" @click="mobileMenuOpen = false" class="block hover:text-orange-400 transition-colors duration-200">About Us</a></li>
                <li><a href="#projects" @click="mobileMenuOpen = false" class="block hover:text-orange-400 transition-colors duration-200">Projects</a></li>
                <li><a href="#gallery" @click="mobileMenuOpen = false" class="block hover:text-orange-400 transition-colors duration-200">Gallery</a></li>
                <li><a href="#contact" @click="mobileMenuOpen = false" class="block hover:text-orange-400 transition-colors duration-200">Contact Us</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Hero Section -->
        <section class="relative min-h-screen flex items-center justify-center bg-cover bg-center bg-gray-800" 
                 style="background-image: linear-gradient(rgba(0,0,0,0.5), rgba(90,58,40,0.4)), url('/public/images/hero-women-helping.jpg');">
            <div class="hero-overlay absolute inset-0"></div>
            <div class="relative z-10 text-center text-white max-w-4xl px-6 py-8">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    SEVO — A Ripple of Hope
                </h1>
                
                <!-- Quranic Verse -->
                <div class="mb-8">
                    <p class="arabic-text text-lg md:text-2xl mb-3 font-semibold">
                        "من فرّج عن مؤمن كربة من كرب الدنيا، فرّج الله عنه كربة من كرب يوم القيامة"
                    </p>
                    <p class="text-sm md:text-base text-gray-200 italic">
                        "Whoever relieves a believer's distress of the distressful aspects of this world, Allah will rescue him from a difficulty of the difficulties of the Hereafter." (Sahih Muslim)
                    </p>
                </div>
                
                <!-- Call to Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#contact" 
                       class="bg-orange-500 hover:bg-orange-600 px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                        Join Our Mission
                    </a>
                    <a href="#projects" 
                       class="bg-blue-500 hover:bg-blue-600 px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                        View Our Impact
                    </a>
                </div>
            </div>
            
            <!-- Scroll Down Indicator -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </div>
        </section>

        <!-- Introduction Blocks Section -->
        <section id="about" class="py-20 bg-sevo-cream">
            <div class="max-w-7xl mx-auto px-6">
                <!-- First Block: Women Empowerment -->
                <div class="grid md:grid-cols-2 gap-12 items-center mb-20 fade-in">
                    <!-- Left image -->
                    <div class="relative">
                        <img src="/public/images/women-sewing.jpg" 
                             alt="Women participating in sewing and embroidery training program" 
                             class="rounded-xl shadow-2xl w-full h-80 object-cover"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVFRkU2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNUEzQTI4IiBmb250LXNpemU9IjE2Ij5Xb21lbiBFbXBvd2VybWVudDwvdGV4dD4KPC9zdmc+'">
                        <div class="absolute inset-0 bg-gradient-to-t from-sevo-brown/20 to-transparent rounded-xl"></div>
                    </div>
                    
                    <!-- Right text -->
                    <div class="space-y-6">
                        <h2 class="text-3xl md:text-4xl font-bold text-sevo-brown leading-tight">
                            Empowering Women Through Skills
                        </h2>
                        <p class="text-gray-700 text-lg leading-relaxed">
                            Since 2009, SEVO has supported women across Darfur by offering vocational training, micro-loans, and hands-on projects in embroidery, dairy, poultry, and more. Through our workshops and field support, we help transform potential into dignity and sustainable income.
                        </p>
                        <div class="flex flex-wrap gap-3 mb-6">
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">Vocational Training</span>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">Micro-loans</span>
                            <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">Embroidery</span>
                            <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">Dairy & Poultry</span>
                        </div>
                        <a href="#contact" 
                           class="inline-block bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Join Our Mission
                        </a>
                    </div>
                </div>

                <!-- Second Block: Community Service (Flipped Layout) -->
                <div class="grid md:grid-cols-2 gap-12 items-center fade-in">
                    <!-- Right image -->
                    <div class="relative md:order-2">
                        <img src="/public/images/well-project.jpg" 
                             alt="SEVO volunteers building water well for displaced community" 
                             class="rounded-xl shadow-2xl w-full h-80 object-cover"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVFRkU2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNUEzQTI4IiBmb250LXNpemU9IjE2Ij5XZWxsIFByb2plY3Q8L3RleHQ+Cjwvc3ZnPg=='">
                        <div class="absolute inset-0 bg-gradient-to-t from-blue-900/20 to-transparent rounded-xl"></div>
                    </div>
                    
                    <!-- Left text -->
                    <div class="space-y-6 md:order-1">
                        <h2 class="text-3xl md:text-4xl font-bold text-sevo-brown leading-tight">
                            Serving Displaced Communities
                        </h2>
                        <p class="text-gray-700 text-lg leading-relaxed">
                            SEVO responds to crises by building water wells, distributing Eid gifts, and rehabilitating orphaned and displaced children. We serve refugee camps and rural villages with compassion, consistency, and unwavering commitment to human dignity.
                        </p>
                        <div class="flex flex-wrap gap-3 mb-6">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">Water Wells</span>
                            <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">Eid Gifts</span>
                            <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">Child Rehabilitation</span>
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">Refugee Support</span>
                        </div>
                        <a href="#contact" 
                           class="inline-block bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Support Our Work
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Photo Gallery Section -->
        <section id="gallery" class="bg-white py-20">
            <div class="max-w-7xl mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-sevo-brown mb-4">Gallery of Impact</h2>
                    <p class="text-gray-600 text-lg max-w-2xl mx-auto">
                        Witness the transformation and hope we bring to communities across Sudan through our various projects and initiatives.
                    </p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Gallery Item 1 -->
                    <div class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                        <img src="/public/images/project1.jpg"
                             alt="Women's embroidery training workshop in progress"
                             class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVFRkU2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNUEzQTI4IiBmb250LXNpemU9IjE0Ij5FbWJyb2lkZXJ5IFRyYWluaW5nPC90ZXh0Pgo8L3N2Zz4='">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div class="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <h3 class="font-semibold text-lg mb-1">Embroidery Training</h3>
                            <p class="text-sm text-gray-200">Empowering women with traditional skills</p>
                        </div>
                    </div>

                    <!-- Gallery Item 2 -->
                    <div class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                        <img src="/public/images/project2.jpg"
                             alt="Water well construction project in rural village"
                             class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVFRkU2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNUEzQTI4IiBmb250LXNpemU9IjE0Ij5XYXRlciBXZWxsIFByb2plY3Q8L3RleHQ+Cjwvc3ZnPg=='">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div class="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <h3 class="font-semibold text-lg mb-1">Water Well Project</h3>
                            <p class="text-sm text-gray-200">Bringing clean water to communities</p>
                        </div>
                    </div>

                    <!-- Gallery Item 3 -->
                    <div class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                        <img src="/public/images/project3.jpg"
                             alt="Eid gift distribution to displaced children"
                             class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVFRkU2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNUEzQTI4IiBmb250LXNpemU9IjE0Ij5FaWQgR2lmdCBEaXN0cmlidXRpb248L3RleHQ+Cjwvc3ZnPg=='">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div class="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <h3 class="font-semibold text-lg mb-1">Eid Gift Distribution</h3>
                            <p class="text-sm text-gray-200">Spreading joy to displaced children</p>
                        </div>
                    </div>

                    <!-- Gallery Item 4 -->
                    <div class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                        <img src="/public/images/project4.jpg"
                             alt="Dairy farming training for women entrepreneurs"
                             class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVFRkU2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNUEzQTI4IiBmb250LXNpemU9IjE0Ij5EYWlyeSBGYXJtaW5nPC90ZXh0Pgo8L3N2Zz4='">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div class="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <h3 class="font-semibold text-lg mb-1">Dairy Farming</h3>
                            <p class="text-sm text-gray-200">Sustainable income generation</p>
                        </div>
                    </div>

                    <!-- Gallery Item 5 -->
                    <div class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                        <img src="/public/images/project5.jpg"
                             alt="Poultry farming project for women's economic empowerment"
                             class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVFRkU2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNUEzQTI4IiBmb250LXNpemU9IjE0Ij5Qb3VsdHJ5IEZhcm1pbmc8L3RleHQ+Cjwvc3ZnPg=='">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div class="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <h3 class="font-semibold text-lg mb-1">Poultry Farming</h3>
                            <p class="text-sm text-gray-200">Economic empowerment through agriculture</p>
                        </div>
                    </div>

                    <!-- Gallery Item 6 -->
                    <div class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                        <img src="/public/images/project6.jpg"
                             alt="Community volunteers working together on rehabilitation project"
                             class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjVFRkU2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNUEzQTI4IiBmb250LXNpemU9IjE0Ij5Db21tdW5pdHkgV29yazwvdGV4dD4KPC9zdmc+'">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div class="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <h3 class="font-semibold text-lg mb-1">Community Work</h3>
                            <p class="text-sm text-gray-200">Volunteers building hope together</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section with Animated Counters -->
        <section id="projects" class="bg-sevo-cream py-20">
            <div class="max-w-7xl mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-sevo-brown mb-4">Sadaqah in Action</h2>
                    <p class="arabic-text text-xl md:text-2xl text-gray-700 mb-2 font-semibold">
                        "خير الناس أنفعهم للناس"
                    </p>
                    <p class="text-gray-600 italic">
                        "The best of people are those who benefit others"
                    </p>
                </div>

                <div class="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
                    <!-- Stat 1: Women Trained -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                         x-data="{ count: 0, target: 500 }"
                         x-intersect:enter="$nextTick(() => {
                             const increment = target / 100;
                             const timer = setInterval(() => {
                                 count += increment;
                                 if (count >= target) {
                                     count = target;
                                     clearInterval(timer);
                                 }
                             }, 20);
                         })">
                        <div class="mb-4">
                            <svg class="w-12 h-12 mx-auto text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <p class="text-4xl md:text-5xl font-bold text-green-600 mb-2" x-text="Math.floor(count) + '+'">0+</p>
                        <p class="text-gray-700 font-medium">Women Trained</p>
                        <p class="text-sm text-gray-500 mt-1">In vocational skills</p>
                    </div>

                    <!-- Stat 2: Wells Built -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                         x-data="{ count: 0, target: 20 }"
                         x-intersect:enter="$nextTick(() => {
                             const increment = target / 50;
                             const timer = setInterval(() => {
                                 count += increment;
                                 if (count >= target) {
                                     count = target;
                                     clearInterval(timer);
                                 }
                             }, 40);
                         })">
                        <div class="mb-4">
                            <svg class="w-12 h-12 mx-auto text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            </svg>
                        </div>
                        <p class="text-4xl md:text-5xl font-bold text-blue-600 mb-2" x-text="Math.floor(count) + '+'">0+</p>
                        <p class="text-gray-700 font-medium">Wells Built</p>
                        <p class="text-sm text-gray-500 mt-1">Providing clean water</p>
                    </div>

                    <!-- Stat 3: Eid Gifts -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                         x-data="{ count: 0, target: 1500 }"
                         x-intersect:enter="$nextTick(() => {
                             const increment = target / 100;
                             const timer = setInterval(() => {
                                 count += increment;
                                 if (count >= target) {
                                     count = target;
                                     clearInterval(timer);
                                 }
                             }, 15);
                         })">
                        <div class="mb-4">
                            <svg class="w-12 h-12 mx-auto text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                            </svg>
                        </div>
                        <p class="text-4xl md:text-5xl font-bold text-orange-600 mb-2" x-text="Math.floor(count) + '+'">0+</p>
                        <p class="text-gray-700 font-medium">Eid Gifts</p>
                        <p class="text-sm text-gray-500 mt-1">Distributed to children</p>
                    </div>

                    <!-- Stat 4: Volunteers -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                         x-data="{ count: 0, target: 80 }"
                         x-intersect:enter="$nextTick(() => {
                             const increment = target / 60;
                             const timer = setInterval(() => {
                                 count += increment;
                                 if (count >= target) {
                                     count = target;
                                     clearInterval(timer);
                                 }
                             }, 30);
                         })">
                        <div class="mb-4">
                            <svg class="w-12 h-12 mx-auto text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <p class="text-4xl md:text-5xl font-bold text-purple-600 mb-2" x-text="Math.floor(count) + '+'">0+</p>
                        <p class="text-gray-700 font-medium">Volunteers</p>
                        <p class="text-sm text-gray-500 mt-1">Dedicated helpers</p>
                    </div>
                </div>

                <!-- Call to Action -->
                <div class="text-center mt-16">
                    <p class="text-lg text-gray-700 mb-6">
                        Join us in making a difference. Every contribution creates ripples of hope.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="#contact"
                           class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Become a Volunteer
                        </a>
                        <a href="#contact"
                           class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Support Our Mission
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Section -->
    <footer id="contact" class="bg-sevo-brown text-white py-16">
        <div class="max-w-7xl mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12">
                <!-- Logo + Intro -->
                <div class="lg:col-span-1">
                    <div class="flex items-center mb-6">
                        <img src="/public/images/logo.svg" alt="SEVO Logo" class="h-12 w-12 mr-3" onerror="this.style.display='none'">
                        <span class="text-2xl font-bold">SEVO</span>
                    </div>
                    <p class="text-gray-300 leading-relaxed mb-6">
                        SEVO (Simat Elkhair) is a voluntary Sudanese organization that empowers women, builds communities, and nurtures lives with faith, compassion, and unwavering commitment to human dignity.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-orange-400 transition-colors duration-200" aria-label="Facebook">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-orange-400 transition-colors duration-200" aria-label="WhatsApp">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-orange-400 transition-colors duration-200" aria-label="Instagram">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.926 3.708 13.775 3.708 12.478s.49-2.448 1.418-3.323C6.001 8.28 7.152 7.79 8.449 7.79s2.448.49 3.323 1.365c.875.875 1.365 2.026 1.365 3.323s-.49 2.448-1.365 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.875-.875-1.365-2.026-1.365-3.323s.49-2.448 1.365-3.323c.875-.875 2.026-1.365 3.323-1.365s2.448.49 3.323 1.365c.875.875 1.365 2.026 1.365 3.323s-.49 2.448-1.365 3.323z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-orange-400 transition-colors duration-200" aria-label="YouTube">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Useful Links -->
                <div>
                    <h4 class="font-bold text-lg mb-6 text-orange-400">Useful Links</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-300 hover:text-orange-400 transition-colors duration-200 flex items-center">
                            <span class="mr-2">→</span> Home
                        </a></li>
                        <li><a href="#about" class="text-gray-300 hover:text-orange-400 transition-colors duration-200 flex items-center">
                            <span class="mr-2">→</span> About Us
                        </a></li>
                        <li><a href="#projects" class="text-gray-300 hover:text-orange-400 transition-colors duration-200 flex items-center">
                            <span class="mr-2">→</span> Our Impact
                        </a></li>
                        <li><a href="#gallery" class="text-gray-300 hover:text-orange-400 transition-colors duration-200 flex items-center">
                            <span class="mr-2">→</span> Gallery
                        </a></li>
                        <li><a href="#contact" class="text-gray-300 hover:text-orange-400 transition-colors duration-200 flex items-center">
                            <span class="mr-2">→</span> Contact Us
                        </a></li>
                    </ul>
                </div>

                <!-- Our Programs -->
                <div>
                    <h4 class="font-bold text-lg mb-6 text-orange-400">Our Programs</h4>
                    <ul class="space-y-3 text-gray-300">
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            Women's Empowerment
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            Water Well Projects
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            Child Rehabilitation
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                            Vocational Training
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-orange-400 rounded-full mr-3"></span>
                            Community Support
                        </li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="font-bold text-lg mb-6 text-orange-400">Contact Info</h4>
                    <div class="space-y-4 text-gray-300">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 mr-3 mt-1 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <div>
                                <p class="font-medium">Nyala, South Darfur</p>
                                <p class="text-sm text-gray-400">Sudan</p>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <p>+249 912 476 429</p>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <p><EMAIL></p>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                            <p>www.sevo.org</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Footer -->
            <div class="border-t border-gray-600 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-center md:text-left mb-4 md:mb-0">
                        <p class="text-gray-300">
                            © 2025 SEVO - Simat Elkhair Voluntary Organization. All rights reserved.
                        </p>
                        <p class="text-sm text-gray-400 mt-1">
                            Built with ❤️ by Mohamed Musa | Empowering communities through faith and action
                        </p>
                    </div>

                    <div class="flex items-center space-x-4 text-sm text-gray-400">
                        <a href="#" class="hover:text-orange-400 transition-colors duration-200">Privacy Policy</a>
                        <span>|</span>
                        <a href="#" class="hover:text-orange-400 transition-colors duration-200">Terms of Service</a>
                        <span>|</span>
                        <a href="#" class="hover:text-orange-400 transition-colors duration-200">Donate</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button id="scrollToTop"
            class="fixed bottom-8 right-8 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 opacity-0 invisible"
            onclick="window.scrollTo({top: 0, behavior: 'smooth'})"
            aria-label="Scroll to top">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- JavaScript for scroll effects and animations -->
    <script>
        // Scroll to top button visibility
        window.addEventListener('scroll', function() {
            const scrollToTopBtn = document.getElementById('scrollToTop');
            if (window.pageYOffset > 300) {
                scrollToTopBtn.classList.remove('opacity-0', 'invisible');
                scrollToTopBtn.classList.add('opacity-100', 'visible');
            } else {
                scrollToTopBtn.classList.add('opacity-0', 'invisible');
                scrollToTopBtn.classList.remove('opacity-100', 'visible');
            }
        });

        // Fade in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80; // Account for fixed navbar
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>
